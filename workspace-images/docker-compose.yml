# Docker Compose for Omnispace Workspace Images Testing
# This file is for testing workspace images locally

version: '3.8'

services:
  # Ubuntu Desktop Workspace
  ubuntu-desktop:
    build:
      context: ./ubuntu-desktop
      dockerfile: Dockerfile
    image: omnispace/ubuntu-desktop:latest
    container_name: test-ubuntu-desktop
    ports:
      - "5901:5901"
    environment:
      - VNC_PASSWORD=test123
      - DISPLAY_WIDTH=1920
      - DISPLAY_HEIGHT=1080
      - USER_NAME=workspace
    volumes:
      - ubuntu-desktop-home:/home/<USER>
    restart: unless-stopped
    profiles:
      - ubuntu

  # Development Environment Workspace
  development-env:
    build:
      context: ./development-env
      dockerfile: Dockerfile
    image: omnispace/development-env:latest
    container_name: test-development-env
    ports:
      - "5902:5901"
    environment:
      - VNC_PASSWORD=test123
      - DISPLAY_WIDTH=1920
      - DISPLAY_HEIGHT=1080
      - USER_NAME=workspace
    volumes:
      - development-env-home:/home/<USER>
      - /var/run/docker.sock:/var/run/docker.sock  # Enable Docker-in-Docker
    restart: unless-stopped
    profiles:
      - development

  # Minimal Desktop Workspace
  minimal-desktop:
    build:
      context: ./minimal-desktop
      dockerfile: Dockerfile
    image: omnispace/minimal-desktop:latest
    container_name: test-minimal-desktop
    ports:
      - "5903:5901"
    environment:
      - VNC_PASSWORD=test123
      - DISPLAY_WIDTH=1280
      - DISPLAY_HEIGHT=720
      - USER_NAME=workspace
    volumes:
      - minimal-desktop-home:/home/<USER>
    restart: unless-stopped
    profiles:
      - minimal

  # Apache Guacamole for testing (optional)
  guacamole-db:
    image: postgres:15
    container_name: guacamole-postgres
    environment:
      POSTGRES_DB: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
    volumes:
      - guacamole-db-data:/var/lib/postgresql/data
    profiles:
      - guacamole

  guacd:
    image: guacamole/guacd:1.5.5
    container_name: guacd
    restart: unless-stopped
    profiles:
      - guacamole

  guacamole:
    image: guacamole/guacamole:1.5.5
    container_name: guacamole
    depends_on:
      - guacamole-db
      - guacd
    ports:
      - "8080:8080"
    environment:
      GUACD_HOSTNAME: guacd
      POSTGRES_HOSTNAME: guacamole-db
      POSTGRES_DATABASE: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
      WEBAPP_CONTEXT: ROOT
    profiles:
      - guacamole

volumes:
  ubuntu-desktop-home:
  development-env-home:
  minimal-desktop-home:
  guacamole-db-data:

networks:
  default:
    name: omnispace-workspace-network
