#!/bin/bash
set -e

echo "Building Omnispace Workspace Images..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Build Ubuntu Desktop image
print_status "Building Ubuntu Desktop workspace image..."
cd ubuntu-desktop
if docker build -t omnispace/ubuntu-desktop:latest .; then
    print_success "Ubuntu Desktop image built successfully"
else
    print_error "Failed to build Ubuntu Desktop image"
    exit 1
fi
cd ..

# Build Development Environment image
print_status "Building Development Environment workspace image..."
cd development-env
if docker build -t omnispace/development-env:latest .; then
    print_success "Development Environment image built successfully"
else
    print_error "Failed to build Development Environment image"
    exit 1
fi
cd ..

# Build Minimal Desktop image
print_status "Building Minimal Desktop workspace image..."
cd minimal-desktop
if docker build -t omnispace/minimal-desktop:latest .; then
    print_success "Minimal Desktop image built successfully"
else
    print_error "Failed to build Minimal Desktop image"
    exit 1
fi
cd ..

# Tag images with version
VERSION=$(date +%Y%m%d)
print_status "Tagging images with version: $VERSION"

docker tag omnispace/ubuntu-desktop:latest omnispace/ubuntu-desktop:$VERSION
docker tag omnispace/development-env:latest omnispace/development-env:$VERSION
docker tag omnispace/minimal-desktop:latest omnispace/minimal-desktop:$VERSION

print_success "All workspace images built successfully!"

# Display image information
print_status "Built images:"
docker images | grep omnispace

echo ""
print_status "To test an image, run:"
echo "docker run -d -p 5901:5901 -e VNC_PASSWORD=test123 omnispace/ubuntu-desktop:latest"
echo ""
print_status "To push images to registry (if configured):"
echo "./push-images.sh"
