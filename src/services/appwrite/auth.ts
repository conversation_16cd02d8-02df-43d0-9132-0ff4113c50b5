import {
  adminAccount,
  adminUsers,
  createSessionServices,
  AppwriteServerError,
  logger,
  ID
} from '@/lib/appwrite-server';
import { BaseAppwriteService, ServiceResult } from './base';
import { UserProfile, UserSession, OAuthProvider } from '@/lib/appwrite';


// Authentication interfaces
export interface CreateUserParams {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

export interface LoginParams {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface UpdateUserParams {
  name?: string;
  email?: string;
  phone?: string;
  bio?: string;
  company?: string;
  location?: string;
  website?: string;
  password?: string; // Required for email/phone updates
}

export interface PasswordResetParams {
  email: string;
  url: string;
}

export interface PasswordUpdateParams {
  currentPassword: string;
  newPassword: string;
}

export interface TwoFactorSetupParams {
  userId: string;
  secret?: string;
}

export interface TwoFactorVerifyParams {
  userId: string;
  token: string;
}

export interface SessionInfo {
  sessionId: string;
  userId: string;
  deviceInfo: {
    userAgent: string;
    ip: string;
    location?: string;
  };
  expiresAt: string;
  isActive: boolean;
}

// Helper function to convert Appwrite User to UserProfile
function convertUserToProfile(user: any): UserProfile {
  return {
    $id: user.$id,
    $createdAt: user.$createdAt,
    $updatedAt: user.$updatedAt,
    userId: user.$id,
    name: user.name,
    email: user.email,
    preferences: {
      theme: 'system',
      language: 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      notifications: {
        email: true,
        push: true,
        vmUpdates: true,
        securityAlerts: true,
      },
      dashboard: {
        defaultView: 'grid',
        itemsPerPage: 12,
        autoRefresh: true,
      },
    },
    subscription: {
      plan: 'starter',
      status: 'active',
    },
    usage: {
      vmsCreated: 0,
      storageUsed: 0,
      computeHours: 0,
      lastActivity: new Date().toISOString(),
    },
    security: {
      twoFactorEnabled: false,
      lastPasswordChange: new Date().toISOString(),
      loginSessions: 0,
    },
  };
}

// Authentication service class
export class AuthService extends BaseAppwriteService {
  constructor() {
    super('AuthService');
  }

  // Create new user account
  async createUser(params: CreateUserParams): Promise<ServiceResult<UserProfile>> {
    return this.executeOperation('createUser', async () => {
      this.validateRequired(params, ['email', 'password', 'name']);

      // Create user with Appwrite
      const user = await adminUsers.create(
        ID.unique(),
        params.email,
        params.phone,
        params.password,
        params.name
      );

      logger.info(`User created successfully: ${user.$id}`);

      // Return a basic user profile structure
      const userProfile: UserProfile = {
        $id: user.$id,
        $createdAt: user.$createdAt,
        $updatedAt: user.$updatedAt,
        userId: user.$id,
        name: user.name,
        email: user.email,
        preferences: {
          theme: 'system',
          language: 'en',
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          notifications: {
            email: true,
            push: true,
            vmUpdates: true,
            securityAlerts: true,
          },
          dashboard: {
            defaultView: 'grid',
            itemsPerPage: 12,
            autoRefresh: true,
          },
        },
        subscription: {
          plan: 'starter',
          status: 'active',
        },
        usage: {
          vmsCreated: 0,
          storageUsed: 0,
          computeHours: 0,
          lastActivity: new Date().toISOString(),
        },
        security: {
          twoFactorEnabled: false,
          lastPasswordChange: new Date().toISOString(),
          loginSessions: 0,
        },
      };

      return userProfile;
    });
  }

  // Authenticate user and create session
  async loginUser(params: LoginParams): Promise<ServiceResult<{ user: UserProfile; session: SessionInfo }>> {
    return this.executeOperation('loginUser', async () => {
      this.validateRequired(params, ['email', 'password']);

      // Create session using session client
      const sessionServices = createSessionServices();
      const session = await sessionServices.account.createEmailPasswordSession(
        params.email,
        params.password
      );

      // Get user details
      const user = await sessionServices.account.get();
      
      // Create session info
      const sessionInfo: SessionInfo = {
        sessionId: session.$id,
        userId: user.$id,
        deviceInfo: {
          userAgent: 'Server-side',
          ip: '127.0.0.1', // This would be extracted from request in real implementation
        },
        expiresAt: session.expire,
        isActive: true,
      };

      logger.info(`User logged in successfully: ${user.$id}`);

      return {
        user: convertUserToProfile(user),
        session: sessionInfo,
      };
    });
  }

  // Logout user and invalidate session
  async logoutUser(sessionId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('logoutUser', async () => {
      this.validateRequired({ sessionId }, ['sessionId']);

      const sessionServices = createSessionServices(sessionId);
      await sessionServices.account.deleteSession('current');

      logger.info(`User logged out successfully: ${sessionId}`);
      return true;
    });
  }

  // Get current user from session
  async getCurrentUser(sessionId: string): Promise<ServiceResult<UserProfile>> {
    return this.executeOperation('getCurrentUser', async () => {
      this.validateRequired({ sessionId }, ['sessionId']);

      const sessionServices = createSessionServices(sessionId);
      const user = await sessionServices.account.get();

      return convertUserToProfile(user);
    });
  }

  // Update user profile
  async updateUser(sessionId: string, params: UpdateUserParams): Promise<ServiceResult<UserProfile>> {
    return this.executeOperation('updateUser', async () => {
      this.validateRequired({ sessionId }, ['sessionId']);

      const sessionServices = createSessionServices(sessionId);
      const sanitizedParams = this.sanitizeInput(params);

      // Update user account
      if (sanitizedParams.name) {
        await sessionServices.account.updateName(sanitizedParams.name);
      }

      if (sanitizedParams.email) {
        await sessionServices.account.updateEmail(sanitizedParams.email, params.password || '');
      }

      if (sanitizedParams.phone) {
        await sessionServices.account.updatePhone(sanitizedParams.phone, params.password || '');
      }

      // Get updated user
      const updatedUser = await sessionServices.account.get();

      logger.info(`User updated successfully: ${updatedUser.$id}`);
      return convertUserToProfile(updatedUser);
    });
  }

  // Change user password
  async updatePassword(sessionId: string, params: PasswordUpdateParams): Promise<ServiceResult<boolean>> {
    return this.executeOperation('updatePassword', async () => {
      this.validateRequired({ sessionId, ...params }, ['sessionId', 'currentPassword', 'newPassword']);

      const sessionServices = createSessionServices(sessionId);
      await sessionServices.account.updatePassword(params.newPassword, params.currentPassword);

      logger.info(`Password updated successfully for session: ${sessionId}`);
      return true;
    });
  }

  // Send password reset email
  async sendPasswordReset(params: PasswordResetParams): Promise<ServiceResult<boolean>> {
    return this.executeOperation('sendPasswordReset', async () => {
      this.validateRequired(params, ['email', 'url']);

      await adminAccount.createRecovery(params.email, params.url);

      logger.info(`Password reset email sent to: ${params.email}`);
      return true;
    });
  }

  // Verify email address
  async verifyEmail(sessionId: string, secret: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('verifyEmail', async () => {
      this.validateRequired({ sessionId, secret }, ['sessionId', 'secret']);

      const sessionServices = createSessionServices(sessionId);
      const user = await sessionServices.account.get();
      await sessionServices.account.updateVerification(user.$id, secret);

      logger.info(`Email verified successfully for session: ${sessionId}`);
      return true;
    });
  }

  // Send email verification
  async sendEmailVerification(sessionId: string, url: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('sendEmailVerification', async () => {
      this.validateRequired({ sessionId, url }, ['sessionId', 'url']);

      const sessionServices = createSessionServices(sessionId);
      await sessionServices.account.createVerification(url);

      logger.info(`Email verification sent for session: ${sessionId}`);
      return true;
    });
  }

  // Get user sessions
  async getUserSessions(sessionId: string): Promise<ServiceResult<SessionInfo[]>> {
    return this.executeOperation('getUserSessions', async () => {
      this.validateRequired({ sessionId }, ['sessionId']);

      const sessionServices = createSessionServices(sessionId);
      const sessions = await sessionServices.account.listSessions();

      const sessionInfos: SessionInfo[] = sessions.sessions.map(session => ({
        sessionId: session.$id,
        userId: session.userId,
        deviceInfo: {
          userAgent: session.clientName || 'Unknown',
          ip: session.ip || 'Unknown',
          location: session.countryName || undefined,
        },
        expiresAt: session.expire,
        isActive: session.current,
      }));

      return sessionInfos;
    });
  }

  // Delete specific session
  async deleteSession(sessionId: string, targetSessionId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteSession', async () => {
      this.validateRequired({ sessionId, targetSessionId }, ['sessionId', 'targetSessionId']);

      const sessionServices = createSessionServices(sessionId);
      await sessionServices.account.deleteSession(targetSessionId);

      logger.info(`Session deleted: ${targetSessionId}`);
      return true;
    });
  }

  // Delete all sessions except current
  async deleteAllSessions(sessionId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteAllSessions', async () => {
      this.validateRequired({ sessionId }, ['sessionId']);

      const sessionServices = createSessionServices(sessionId);
      await sessionServices.account.deleteSessions();

      logger.info(`All sessions deleted except current for session: ${sessionId}`);
      return true;
    });
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await adminUsers.list();
      return true;
    } catch (error) {
      logger.error('Auth service health check failed', error);
      return false;
    }
  }
}
