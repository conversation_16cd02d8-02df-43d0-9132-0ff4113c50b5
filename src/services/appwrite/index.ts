// Export all Appwrite services
export { BaseAppwriteService, connectionPool } from './base';
export { AuthService } from './auth';
export { DatabaseService } from './database';
export { StorageService } from './storage';
export { FunctionsService } from './functions';

// Export types
export type {
  ServiceResult,
  PaginationParams,
  PaginatedResult,
  QueryParams
} from './base';

export type {
  CreateUserParams,
  LoginParams,
  UpdateUserParams,
  PasswordResetParams,
  PasswordUpdateParams,
  SessionInfo
} from './auth';

export type {
  DatabaseInfo,
  CollectionInfo,
  AttributeInfo,
  IndexInfo,
  CreateDatabaseParams,
  CreateCollectionParams,
  CreateAttributeParams,
  CreateIndexParams,
  DocumentData,
  CreateDocumentParams,
  UpdateDocumentParams,
  QueryBuilder
} from './database';

export type {
  BucketInfo,
  FileInfo,
  CreateBucketParams,
  UpdateBucketParams,
  UploadFileParams,
  FileUploadResult,
  ImageTransformOptions
} from './storage';

export type {
  FunctionInfo,
  DeploymentInfo,
  ExecutionInfo,
  CreateFunctionParams,
  UpdateFunctionParams,
  CreateDeploymentParams,
  ExecuteFunctionParams,
  FunctionVariable
} from './functions';

// Service factory class
export class AppwriteServiceFactory {
  private static authService: AuthService;
  private static databaseService: DatabaseService;
  private static storageService: StorageService;
  private static functionsService: FunctionsService;

  // Get auth service instance
  static getAuthService(): AuthService {
    if (!this.authService) {
      this.authService = new AuthService();
    }
    return this.authService;
  }

  // Get database service instance
  static getDatabaseService(): DatabaseService {
    if (!this.databaseService) {
      this.databaseService = new DatabaseService();
    }
    return this.databaseService;
  }

  // Get storage service instance
  static getStorageService(): StorageService {
    if (!this.storageService) {
      this.storageService = new StorageService();
    }
    return this.storageService;
  }

  // Get functions service instance
  static getFunctionsService(): FunctionsService {
    if (!this.functionsService) {
      this.functionsService = new FunctionsService();
    }
    return this.functionsService;
  }

  // Get all services
  static getAllServices() {
    return {
      auth: this.getAuthService(),
      database: this.getDatabaseService(),
      storage: this.getStorageService(),
      functions: this.getFunctionsService()
    };
  }

  // Health check for all services
  static async healthCheckAll(): Promise<{
    overall: boolean;
    services: {
      auth: boolean;
      database: boolean;
      storage: boolean;
      functions: boolean;
    };
    timestamp: string;
  }> {
    const services = this.getAllServices();
    
    const [authHealth, dbHealth, storageHealth, functionsHealth] = await Promise.all([
      services.auth.healthCheck().catch(() => false),
      services.database.healthCheck().catch(() => false),
      services.storage.healthCheck().catch(() => false),
      services.functions.healthCheck().catch(() => false)
    ]);

    const overall = authHealth && dbHealth && storageHealth && functionsHealth;

    return {
      overall,
      services: {
        auth: authHealth,
        database: dbHealth,
        storage: storageHealth,
        functions: functionsHealth
      },
      timestamp: new Date().toISOString()
    };
  }
}

// Convenience exports for direct service access
export const authService = AppwriteServiceFactory.getAuthService();
export const databaseService = AppwriteServiceFactory.getDatabaseService();
export const storageService = AppwriteServiceFactory.getStorageService();
export const functionsService = AppwriteServiceFactory.getFunctionsService();

// Export factory
export default AppwriteServiceFactory;
