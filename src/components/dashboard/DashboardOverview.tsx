'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Monitor, 
  Activity, 
  Cpu, 
  HardDrive, 
  Plus, 
  TrendingUp,
  Users,
  Clock,
  ArrowRight,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Play,
  Square
} from 'lucide-react';
import { WorkspaceInfo } from '@/services/docker';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { StatsCard, QuickActions } from './widgets';

export const DashboardOverview: React.FC = () => {
  const [workspaces, setWorkspaces] = useState<WorkspaceInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalWorkspaces: 0,
    runningWorkspaces: 0,
    stoppedWorkspaces: 0,
    totalCpuUsage: 0,
    totalMemoryUsage: 0,
    systemHealth: 'healthy' as 'healthy' | 'warning' | 'critical'
  });

  // Mock user ID - in real app, this would come from auth context
  const userId = 'demo-user';

  // Fetch workspaces and calculate stats
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workspaces?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        const workspaceData = data.data as WorkspaceInfo[];
        setWorkspaces(workspaceData);
        
        // Calculate stats
        const running = workspaceData.filter(w => w.status === 'running');
        const stopped = workspaceData.filter(w => w.status === 'stopped');
        const totalCpu = running.reduce((sum, w) => sum + w.resources.cpuLimit, 0);
        const totalMemory = running.reduce((sum, w) => sum + w.resources.memoryLimit, 0);
        
        setStats({
          totalWorkspaces: workspaceData.length,
          runningWorkspaces: running.length,
          stoppedWorkspaces: stopped.length,
          totalCpuUsage: totalCpu,
          totalMemoryUsage: totalMemory,
          systemHealth: totalCpu > 80 || totalMemory > 8192 ? 'warning' : 'healthy'
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    
    // Set up periodic refresh
    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [userId]);

  const getHealthIcon = () => {
    switch (stats.systemHealth) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'critical': return <AlertTriangle className="h-5 w-5 text-red-500" />;
    }
  };

  const getWorkspaceTypeIcon = (type: string) => {
    switch (type) {
      case 'ubuntu-desktop': return '🖥️';
      case 'development-env': return '💻';
      case 'minimal-desktop': return '⚡';
      default: return '🖥️';
    }
  };

  const recentWorkspaces = workspaces
    .sort((a, b) => b.created.getTime() - a.created.getTime())
    .slice(0, 5);

  return (
    <div className="flex flex-1 flex-col gap-4 p-6">{/* Removed header - now handled by DashboardHeader */}

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Workspaces"
          value={stats.totalWorkspaces}
          description={`${stats.runningWorkspaces} running, ${stats.stoppedWorkspaces} stopped`}
          icon={Monitor}
        />

        <StatsCard
          title="Active Workspaces"
          value={stats.runningWorkspaces}
          description="Currently running"
          icon={Activity}
          badge={{ text: 'LIVE', variant: 'default' }}
        />

        <StatsCard
          title="CPU Usage"
          value={stats.totalCpuUsage}
          description="Total cores allocated"
          icon={Cpu}
          trend={{
            value: 12,
            label: 'vs last hour',
            direction: 'up'
          }}
        />

        <StatsCard
          title="Memory Usage"
          value={`${(stats.totalMemoryUsage / 1024).toFixed(1)}GB`}
          description="Total memory allocated"
          icon={HardDrive}
          trend={{
            value: 5,
            label: 'vs last hour',
            direction: 'down'
          }}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getHealthIcon()}
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">CPU Load</span>
                <span className="text-sm text-gray-600">
                  {((stats.totalCpuUsage / 16) * 100).toFixed(0)}%
                </span>
              </div>
              <Progress value={(stats.totalCpuUsage / 16) * 100} className="h-2" />
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Memory Usage</span>
                <span className="text-sm text-gray-600">
                  {((stats.totalMemoryUsage / 16384) * 100).toFixed(0)}%
                </span>
              </div>
              <Progress value={(stats.totalMemoryUsage / 16384) * 100} className="h-2" />
            </div>
            <div className="pt-2">
              <Badge variant={stats.systemHealth === 'healthy' ? 'default' : 'destructive'}>
                {stats.systemHealth.toUpperCase()}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Recent Workspaces */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Recent Workspaces</CardTitle>
            <Link href="/dashboard/workspaces">
              <Button variant="ghost" size="sm">
                View All
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            {recentWorkspaces.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                <Monitor className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No workspaces yet</p>
                <Link href="/dashboard/workspaces">
                  <Button size="sm" className="mt-2">
                    <Plus className="h-4 w-4 mr-1" />
                    Create First Workspace
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-3">
                {recentWorkspaces.map((workspace) => (
                  <div key={workspace.id} className="flex items-center justify-between p-2 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <span className="text-lg">
                        {getWorkspaceTypeIcon(workspace.workspaceType)}
                      </span>
                      <div>
                        <p className="font-medium text-sm">{workspace.name}</p>
                        <p className="text-xs text-gray-600">{workspace.userId}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={workspace.status === 'running' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {workspace.status}
                      </Badge>
                      {workspace.status === 'running' ? (
                        <Square className="h-3 w-3 text-gray-400" />
                      ) : (
                        <Play className="h-3 w-3 text-gray-400" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <QuickActions
          actions={[
            {
              title: 'Create New Workspace',
              description: 'Launch a new desktop environment',
              href: '/dashboard/workspaces',
              icon: Plus,
              variant: 'default'
            },
            {
              title: 'View Monitoring',
              description: 'Check system performance metrics',
              href: '/dashboard/monitoring',
              icon: TrendingUp
            },
            {
              title: 'Manage Containers',
              description: 'View and manage Docker containers',
              href: '/dashboard/containers',
              icon: HardDrive
            },
            {
              title: 'User Settings',
              description: 'Configure preferences and limits',
              href: '/dashboard/settings',
              icon: Users
            }
          ]}
        />
      </div>
    </div>
  );
};
