'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { AnimatedButton } from '@/components/ui/enhanced-card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  FileText,
  Download,
  RefreshCw,
  Copy,
  Search,
  Filter,
  Calendar,
  Loader2
} from 'lucide-react';
import { ContainerInfo } from '@/services/docker';

interface ContainerLogsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  container: ContainerInfo | null;
  logs: string;
  onRefreshLogs: (containerId: string, tail?: number) => Promise<void>;
}

export const ContainerLogsDialog: React.FC<ContainerLogsDialogProps> = ({
  open,
  onOpenChange,
  container,
  logs,
  onRefreshLogs
}) => {
  const [tailLines, setTailLines] = useState<number>(100);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [logLevel, setLogLevel] = useState<string>('all');

  const handleRefresh = async () => {
    if (!container) return;
    
    setRefreshing(true);
    try {
      await onRefreshLogs(container.id, tailLines);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCopyLogs = async () => {
    try {
      await navigator.clipboard.writeText(logs);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy logs:', error);
    }
  };

  const handleDownloadLogs = () => {
    if (!container) return;
    
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${container.name}-logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Filter logs based on search term and log level
  const filteredLogs = React.useMemo(() => {
    if (!logs) return '';
    
    let filtered = logs;
    
    // Filter by search term
    if (searchTerm) {
      const lines = logs.split('\n');
      const matchingLines = lines.filter(line => 
        line.toLowerCase().includes(searchTerm.toLowerCase())
      );
      filtered = matchingLines.join('\n');
    }
    
    // Filter by log level
    if (logLevel !== 'all') {
      const lines = filtered.split('\n');
      const levelPattern = new RegExp(`\\b${logLevel}\\b`, 'i');
      const matchingLines = lines.filter(line => levelPattern.test(line));
      filtered = matchingLines.join('\n');
    }
    
    return filtered;
  }, [logs, searchTerm, logLevel]);

  // Parse log lines for syntax highlighting
  const formatLogLine = (line: string, index: number) => {
    const timestampMatch = line.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    const levelMatch = line.match(/\b(ERROR|WARN|INFO|DEBUG|TRACE)\b/i);
    
    let className = 'font-mono text-sm';
    if (levelMatch) {
      const level = levelMatch[0].toUpperCase();
      switch (level) {
        case 'ERROR':
          className += ' text-red-400';
          break;
        case 'WARN':
          className += ' text-yellow-400';
          break;
        case 'INFO':
          className += ' text-blue-400';
          break;
        case 'DEBUG':
          className += ' text-green-400';
          break;
        default:
          className += ' text-gray-300';
      }
    }

    return (
      <div key={index} className={className}>
        {timestampMatch && (
          <span className="text-gray-500 mr-2">
            {timestampMatch[0]}
          </span>
        )}
        {line.replace(timestampMatch?.[0] || '', '').trim()}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Container Logs: {container?.name}
          </DialogTitle>
          <DialogDescription>
            View and analyze logs from your container
          </DialogDescription>
        </DialogHeader>

        {/* Controls */}
        <div className="flex flex-wrap items-center gap-3 py-4 border-b">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Lines:</label>
            <Select value={tailLines.toString()} onValueChange={(value) => setTailLines(parseInt(value))}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="500">500</SelectItem>
                <SelectItem value="1000">1000</SelectItem>
                <SelectItem value="0">All</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Level:</label>
            <Select value={logLevel} onValueChange={setLogLevel}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="warn">Warn</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="debug">Debug</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 text-sm border rounded-md bg-background"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <AnimatedButton
              size="sm"
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
              loading={refreshing}
            >
              <RefreshCw className="h-4 w-4" />
            </AnimatedButton>
            <AnimatedButton
              size="sm"
              variant="outline"
              onClick={handleCopyLogs}
            >
              <Copy className="h-4 w-4" />
            </AnimatedButton>
            <AnimatedButton
              size="sm"
              variant="outline"
              onClick={handleDownloadLogs}
            >
              <Download className="h-4 w-4" />
            </AnimatedButton>
          </div>
        </div>

        {/* Log Content */}
        <div className="flex-1 min-h-0">
          <ScrollArea className="h-full w-full rounded-md border bg-black/5 dark:bg-black/20">
            <div className="p-4 space-y-1">
              {filteredLogs ? (
                filteredLogs.split('\n').map((line, index) => formatLogLine(line, index))
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No logs available</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
