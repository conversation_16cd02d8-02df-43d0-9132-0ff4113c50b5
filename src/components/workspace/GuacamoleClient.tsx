'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Monitor, 
  Power, 
  Maximize2, 
  Minimize2, 
  ExternalLink,
  RefreshCw,
  Settings,
  Keyboard,
  Mouse,
  Volume2,
  Wifi
} from 'lucide-react';
import { WorkspaceInfo } from '@/services/docker';

interface GuacamoleClientProps {
  workspace: WorkspaceInfo;
  onDisconnect?: () => void;
}

export const GuacamoleClient: React.FC<GuacamoleClientProps> = ({ 
  workspace, 
  onDisconnect 
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Guacamole connection URL
  const getGuacamoleUrl = () => {
    const baseUrl = process.env.NEXT_PUBLIC_GUACAMOLE_URL || 'http://localhost:8080';
    
    if (workspace.guacamoleConnectionId) {
      // Direct connection URL with connection ID
      return `${baseUrl}/#/client/${workspace.guacamoleConnectionId}`;
    } else {
      // Fallback to manual connection (should not happen in normal flow)
      return `${baseUrl}`;
    }
  };

  // Initialize connection
  useEffect(() => {
    if (workspace.status === 'running' && workspace.guacamoleConnectionId) {
      connect();
    }
  }, [workspace.status, workspace.guacamoleConnectionId]);

  const connect = async () => {
    if (connecting || connected) return;

    setConnecting(true);
    setError(null);

    try {
      // Check if workspace is running
      if (workspace.status !== 'running') {
        throw new Error('Workspace is not running');
      }

      if (!workspace.guacamoleConnectionId) {
        throw new Error('No Guacamole connection available');
      }

      // Set up iframe load handler
      if (iframeRef.current) {
        iframeRef.current.onload = () => {
          setConnected(true);
          setConnecting(false);
        };

        iframeRef.current.onerror = () => {
          setError('Failed to load Guacamole interface');
          setConnecting(false);
        };

        // Load Guacamole interface
        iframeRef.current.src = getGuacamoleUrl();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection failed');
      setConnecting(false);
    }
  };

  const disconnect = () => {
    if (iframeRef.current) {
      iframeRef.current.src = 'about:blank';
    }
    setConnected(false);
    setConnecting(false);
    onDisconnect?.();
  };

  const toggleFullscreen = () => {
    if (!fullscreen) {
      // Enter fullscreen
      if (iframeRef.current?.requestFullscreen) {
        iframeRef.current.requestFullscreen();
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setFullscreen(!fullscreen);
  };

  const openInNewTab = () => {
    const url = getGuacamoleUrl();
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const refresh = () => {
    if (iframeRef.current && connected) {
      iframeRef.current.src = iframeRef.current.src;
    } else {
      connect();
    }
  };

  const getWorkspaceTypeIcon = (type: string) => {
    switch (type) {
      case 'ubuntu-desktop': return '🖥️';
      case 'development-env': return '💻';
      case 'minimal-desktop': return '⚡';
      default: return '🖥️';
    }
  };

  const getWorkspaceTypeName = (type: string) => {
    switch (type) {
      case 'ubuntu-desktop': return 'Ubuntu Desktop';
      case 'development-env': return 'Development Environment';
      case 'minimal-desktop': return 'Minimal Desktop';
      default: return type;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-none">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-xl">
                {getWorkspaceTypeIcon(workspace.workspaceType)}
              </span>
              <div>
                <CardTitle className="text-lg">{workspace.name}</CardTitle>
                <p className="text-sm text-gray-600">
                  {getWorkspaceTypeName(workspace.workspaceType)} • {workspace.userId}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={workspace.status === 'running' ? 'default' : 'secondary'}>
                {workspace.status}
              </Badge>
              <Badge variant="outline">
                {workspace.displayWidth}x{workspace.displayHeight}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Monitor className="h-4 w-4" />
                <span>Port {workspace.vncPort}</span>
              </div>
              <div className="flex items-center gap-1">
                <Wifi className="h-4 w-4" />
                <span>{connected ? 'Connected' : 'Disconnected'}</span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={refresh}
                disabled={connecting}
              >
                <RefreshCw className={`h-4 w-4 ${connecting ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={openInNewTab}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={toggleFullscreen}
                disabled={!connected}
              >
                {fullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
              
              <Button
                size="sm"
                variant="destructive"
                onClick={disconnect}
              >
                <Power className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex-1 relative bg-black rounded-lg overflow-hidden mt-4">
        {error ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Connection Error</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={connect}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Connection
              </Button>
            </div>
          </div>
        ) : connecting ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Connecting to Workspace</h3>
              <p className="text-gray-600">Please wait while we establish the connection...</p>
            </div>
          </div>
        ) : !connected ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Ready to Connect</h3>
              <p className="text-gray-600 mb-4">
                Click connect to access your {getWorkspaceTypeName(workspace.workspaceType)} environment
              </p>
              <Button onClick={connect}>
                <Monitor className="h-4 w-4 mr-2" />
                Connect to Workspace
              </Button>
            </div>
          </div>
        ) : null}

        <iframe
          ref={iframeRef}
          className={`w-full h-full border-0 ${connected ? 'block' : 'hidden'}`}
          title={`${workspace.name} - Remote Desktop`}
          allow="clipboard-read; clipboard-write"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals"
        />
      </div>

      {connected && (
        <div className="flex-none mt-2">
          <div className="flex items-center justify-center gap-4 text-xs text-gray-500 bg-gray-50 rounded-lg p-2">
            <div className="flex items-center gap-1">
              <Keyboard className="h-3 w-3" />
              <span>Keyboard enabled</span>
            </div>
            <div className="flex items-center gap-1">
              <Mouse className="h-3 w-3" />
              <span>Mouse enabled</span>
            </div>
            <div className="flex items-center gap-1">
              <Volume2 className="h-3 w-3" />
              <span>Audio supported</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
