import { NextRequest, NextResponse } from 'next/server';

export interface WorkspaceTemplate {
  id: string;
  name: string;
  description: string;
  image: string;
  category: 'desktop' | 'development' | 'minimal';
  defaultResources: {
    cpu: number;
    memory: number; // in MB
    storage: number; // in GB
  };
  features: string[];
  ports: {
    vnc: number;
    [key: string]: number;
  };
  environment: {
    [key: string]: string;
  };
  icon: string;
  tags: string[];
  estimatedStartTime: number; // in seconds
  popularity: number;
  lastUpdated: string;
}

// Predefined workspace templates
const WORKSPACE_TEMPLATES: WorkspaceTemplate[] = [
  {
    id: 'ubuntu-desktop',
    name: 'Ubuntu Desktop',
    description: 'Full Ubuntu 22.04 LTS desktop environment with GNOME and essential applications',
    image: 'omnispace/ubuntu-desktop:latest',
    category: 'desktop',
    defaultResources: {
      cpu: 2,
      memory: 2048,
      storage: 20,
    },
    features: [
      'GNOME Desktop Environment',
      'Firefox Web Browser',
      'LibreOffice Suite',
      'File Manager (Nautilus)',
      'Terminal Access',
      'Basic Development Tools',
      'VNC Remote Access',
    ],
    ports: {
      vnc: 5901,
    },
    environment: {
      DISPLAY_WIDTH: '1920',
      DISPLAY_HEIGHT: '1080',
      VNC_DEPTH: '24',
    },
    icon: '🖥️',
    tags: ['desktop', 'ubuntu', 'gnome', 'office'],
    estimatedStartTime: 45,
    popularity: 85,
    lastUpdated: '2025-01-29',
  },
  {
    id: 'development-env',
    name: 'Development Environment',
    description: 'Complete development setup with VS Code, Docker, Node.js, Python, and Git',
    image: 'omnispace/development-env:latest',
    category: 'development',
    defaultResources: {
      cpu: 4,
      memory: 4096,
      storage: 40,
    },
    features: [
      'Visual Studio Code',
      'Docker-in-Docker Support',
      'Node.js (LTS) & npm/pnpm',
      'Python 3 with pip',
      'Git with common configurations',
      'Java OpenJDK',
      'PostgreSQL & Redis clients',
      'Development databases',
      'Terminal with dev tools',
    ],
    ports: {
      vnc: 5901,
    },
    environment: {
      DISPLAY_WIDTH: '1920',
      DISPLAY_HEIGHT: '1080',
      VNC_DEPTH: '24',
    },
    icon: '💻',
    tags: ['development', 'vscode', 'docker', 'nodejs', 'python', 'git'],
    estimatedStartTime: 60,
    popularity: 95,
    lastUpdated: '2025-01-29',
  },
  {
    id: 'minimal-desktop',
    name: 'Minimal Desktop',
    description: 'Lightweight Alpine Linux desktop with XFCE for basic computing needs',
    image: 'omnispace/minimal-desktop:latest',
    category: 'minimal',
    defaultResources: {
      cpu: 1,
      memory: 1024,
      storage: 10,
    },
    features: [
      'XFCE4 Lightweight Desktop',
      'Firefox Web Browser',
      'File Manager (Thunar)',
      'Text Editor (Mousepad)',
      'Terminal Access',
      'Minimal Resource Usage',
      'Fast Startup Time',
    ],
    ports: {
      vnc: 5901,
    },
    environment: {
      DISPLAY_WIDTH: '1280',
      DISPLAY_HEIGHT: '720',
      VNC_DEPTH: '24',
    },
    icon: '⚡',
    tags: ['minimal', 'alpine', 'xfce', 'lightweight'],
    estimatedStartTime: 20,
    popularity: 70,
    lastUpdated: '2025-01-29',
  },
];

// GET /api/workspace-templates - List all available workspace templates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    
    let filteredTemplates = WORKSPACE_TEMPLATES;
    
    // Filter by category if specified
    if (category) {
      filteredTemplates = filteredTemplates.filter(
        template => template.category === category
      );
    }
    
    // Filter by tag if specified
    if (tag) {
      filteredTemplates = filteredTemplates.filter(
        template => template.tags.includes(tag)
      );
    }
    
    // Sort by popularity (descending)
    filteredTemplates.sort((a, b) => b.popularity - a.popularity);
    
    return NextResponse.json({
      success: true,
      data: filteredTemplates,
      count: filteredTemplates.length,
      categories: ['desktop', 'development', 'minimal'],
      availableTags: [...new Set(WORKSPACE_TEMPLATES.flatMap(t => t.tags))],
    });
  } catch (error) {
    console.error('Error listing workspace templates:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list workspace templates',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/workspace-templates - Get template recommendations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { useCase, experience, resources } = body;
    
    let recommendations = [...WORKSPACE_TEMPLATES];
    
    // Filter based on use case
    if (useCase) {
      switch (useCase.toLowerCase()) {
        case 'development':
        case 'coding':
        case 'programming':
          recommendations = recommendations.filter(t => 
            t.category === 'development' || t.tags.includes('development')
          );
          break;
        case 'office':
        case 'productivity':
        case 'documents':
          recommendations = recommendations.filter(t => 
            t.category === 'desktop' || t.tags.includes('office')
          );
          break;
        case 'lightweight':
        case 'basic':
        case 'minimal':
          recommendations = recommendations.filter(t => 
            t.category === 'minimal' || t.tags.includes('lightweight')
          );
          break;
      }
    }
    
    // Filter based on available resources
    if (resources) {
      const { maxCpu, maxMemory } = resources;
      if (maxCpu) {
        recommendations = recommendations.filter(t => 
          t.defaultResources.cpu <= maxCpu
        );
      }
      if (maxMemory) {
        recommendations = recommendations.filter(t => 
          t.defaultResources.memory <= maxMemory
        );
      }
    }
    
    // Sort by popularity and resource efficiency
    recommendations.sort((a, b) => {
      const aScore = b.popularity - (a.defaultResources.cpu + a.defaultResources.memory / 1000);
      const bScore = a.popularity - (b.defaultResources.cpu + b.defaultResources.memory / 1000);
      return bScore - aScore;
    });
    
    return NextResponse.json({
      success: true,
      data: recommendations.slice(0, 3), // Top 3 recommendations
      criteria: { useCase, experience, resources },
      message: 'Template recommendations generated successfully',
    });
  } catch (error) {
    console.error('Error generating template recommendations:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate recommendations',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
