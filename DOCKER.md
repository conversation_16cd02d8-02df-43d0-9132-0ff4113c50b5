# Omnispace Docker Deployment Guide

This guide covers how to deploy Omnispace using Docker and Docker Compose, including the main application and workspace containers.

## Quick Start

### Prerequisites
- Docker 24.0+ and Docker Compose V2
- At least 8GB RAM and 50GB disk space
- Linux host system (recommended for workspace containers)

### 1. Environment Setup
```bash
# Copy environment template
cp .env.docker .env

# Edit environment variables
nano .env

# Important: Change these security-critical values:
# - GUACAMOLE_DB_PASSWORD
# - JWT_SECRET
# - OPENAI_API_KEY (for AI features)
```

### 2. Initialize Guacamole Database
```bash
# Create Guacamole database initialization script
mkdir -p workspace-images/guacamole/init
docker run --rm guacamole/guacamole:1.5.5 /opt/guacamole/bin/initdb.sh --postgresql > workspace-images/guacamole/init/initdb.sql

# Create workspace network
docker network create omnispace-workspace-network
```

### 3. Start Omnispace Platform
```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f omnispace
```

### 4. Access the Platform
- **Omnispace Web UI**: http://localhost:3000
- **Guacamole Interface**: http://localhost:8080 (admin: guacadmin/guacadmin)

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Omnispace     │    │  Apache          │    │  Workspace          │
│   Frontend      │───▶│  Guacamole       │───▶│  Containers         │
│   (Next.js)     │    │  (Web Gateway)   │    │  (Dynamic)          │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
         │                       │                       │
         │              ┌──────────────────┐             │
         └─────────────▶│  PostgreSQL      │◀────────────┘
                        │  Database        │
                        └──────────────────┘
```

## Service Configuration

### Omnispace Application
- **Port**: 3000 (configurable via `OMNISPACE_PORT`)
- **Health Check**: `/api/health` endpoint
- **Docker Socket**: Mounted for container management
- **Networks**: Connected to both omnispace and workspace networks

### Apache Guacamole
- **Port**: 8080 (configurable via `GUACAMOLE_PORT`)
- **Database**: PostgreSQL with persistent storage
- **Features**: VNC/RDP/SSH gateway, session recording, user management

### PostgreSQL Database
- **Version**: PostgreSQL 15
- **Database**: `guacamole_db`
- **Persistent Storage**: Docker volume `omnispace-guacamole-db-data`

## Workspace Management

### Building Workspace Images
```bash
# Build all workspace images
cd workspace-images
./build-all.sh

# Available images:
# - omnispace/ubuntu-desktop:latest
# - omnispace/development-env:latest
# - omnispace/minimal-desktop:latest
```

### Creating Workspace Containers
```bash
# Example: Create Ubuntu Desktop workspace
docker run -d \
  --name workspace-user1-ubuntu \
  --network omnispace-workspace-network \
  -e VNC_PASSWORD=secure123 \
  -e DISPLAY_WIDTH=1920 \
  -e DISPLAY_HEIGHT=1080 \
  omnispace/ubuntu-desktop:latest

# The Omnispace application will automatically:
# 1. Detect the new container
# 2. Register it with Guacamole
# 3. Make it available to users
```

## Production Deployment

### 1. Security Hardening
```bash
# Generate secure passwords
GUACAMOLE_DB_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)

# Update .env file with secure values
echo "GUACAMOLE_DB_PASSWORD=$GUACAMOLE_DB_PASSWORD" >> .env
echo "JWT_SECRET=$JWT_SECRET" >> .env
```

### 2. SSL/TLS Configuration
```bash
# Option 1: Use reverse proxy (recommended)
# Configure nginx/traefik in front of Omnispace

# Option 2: Enable nginx profile in Guacamole
echo "COMPOSE_PROFILES=nginx" >> .env
```

### 3. Resource Limits
```yaml
# Add to docker-compose.override.yml
services:
  omnispace:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### 4. Monitoring and Logging
```bash
# Enable Redis for session management
echo "COMPOSE_PROFILES=redis" >> .env

# Configure log rotation
docker-compose up -d --force-recreate
```

## Backup and Recovery

### Database Backup
```bash
# Create backup script
cat > backup-omnispace.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backups/omnispace"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# Backup Guacamole database
docker exec omnispace-guacamole-db pg_dump -U guacamole_user guacamole_db > \
  $BACKUP_DIR/guacamole_backup_$DATE.sql

# Backup Docker volumes
docker run --rm -v omnispace-guacamole-db-data:/data -v $BACKUP_DIR:/backup \
  alpine tar czf /backup/volumes_backup_$DATE.tar.gz -C /data .

# Cleanup old backups (keep 7 days)
find $BACKUP_DIR -name "*backup_*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*backup_*.tar.gz" -mtime +7 -delete
EOF

chmod +x backup-omnispace.sh

# Schedule daily backups
echo "0 2 * * * /path/to/backup-omnispace.sh" | crontab -
```

### Recovery
```bash
# Restore database
docker exec -i omnispace-guacamole-db psql -U guacamole_user guacamole_db < backup.sql

# Restore volumes
docker run --rm -v omnispace-guacamole-db-data:/data -v /backups:/backup \
  alpine tar xzf /backup/volumes_backup_DATE.tar.gz -C /data
```

## Troubleshooting

### Common Issues

1. **Omnispace won't start**
   ```bash
   # Check logs
   docker-compose logs omnispace
   
   # Verify environment variables
   docker-compose config
   
   # Check Docker socket permissions
   ls -la /var/run/docker.sock
   ```

2. **Guacamole connection failed**
   ```bash
   # Check Guacamole logs
   docker-compose logs guacamole
   
   # Verify database connection
   docker-compose logs guacamole-db
   
   # Test network connectivity
   docker exec omnispace-guacamole ping guacamole-db
   ```

3. **Workspace containers not accessible**
   ```bash
   # Check workspace network
   docker network ls | grep workspace
   
   # Verify container is running
   docker ps | grep workspace
   
   # Test VNC connection
   docker exec workspace-container pgrep Xvnc
   ```

### Performance Optimization

1. **Resource Monitoring**
   ```bash
   # Monitor container resources
   docker stats
   
   # Check disk usage
   docker system df
   
   # Clean up unused resources
   docker system prune -a
   ```

2. **Database Optimization**
   ```bash
   # Optimize PostgreSQL
   docker exec omnispace-guacamole-db psql -U guacamole_user -d guacamole_db -c "VACUUM ANALYZE;"
   ```

## Scaling and High Availability

### Horizontal Scaling
```yaml
# docker-compose.scale.yml
services:
  omnispace:
    deploy:
      replicas: 3
    ports:
      - "3000-3002:3000"
```

### Load Balancing
```bash
# Use nginx or traefik for load balancing
# Example nginx configuration available in docs/nginx.conf
```

This Docker setup provides a complete, production-ready deployment of Omnispace with workspace management capabilities!
